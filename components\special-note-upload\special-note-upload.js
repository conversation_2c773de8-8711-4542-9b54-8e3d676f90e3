Component({
  properties: {
    // 是否显示组件
    show: {
      type: Boolean,
      value: false
    },
    // 订单信息
    orderInfo: {
      type: Object,
      value: {}
    },
    // 是否为只读模式（服务完成后只能查看）
    readonly: {
      type: Boolean,
      value: false
    },
    // 已有的特殊情况说明数据
    noteData: {
      type: Object,
      value: null
    }
  },

  data: {
    // 文字内容
    content: '',
    // 图片列表
    photoList: [],
    // 最大图片数量
    maxCount: 9,
    // 最大文字长度
    maxContentLength: 1000,
    // 是否正在上传
    uploading: false,
    // 是否有数据变更
    hasChanges: false
  },

  observers: {
    'show': function(show) {
      if (show) {
        this.initData();
      }
    },
    'noteData': function(noteData) {
      if (noteData) {
        this.setData({
          content: noteData.content || '',
          photoList: noteData.photos || []
        });
      }
    }
  },

  methods: {
    /**
     * 初始化数据
     */
    initData() {
      const { noteData } = this.data;
      if (noteData) {
        this.setData({
          content: noteData.content || '',
          photoList: noteData.photos || [],
          hasChanges: false
        });
      } else {
        this.setData({
          content: '',
          photoList: [],
          hasChanges: false
        });
      }
    },

    /**
     * 文字内容输入
     */
    onContentInput(e) {
      const content = e.detail.value;
      this.setData({
        content,
        hasChanges: true
      });
    },

    /**
     * 选择图片
     */
    chooseImage() {
      if (this.data.readonly) {
        return;
      }

      const { photoList, maxCount } = this.data;
      const remainCount = maxCount - photoList.length;
      
      if (remainCount <= 0) {
        wx.showToast({
          title: `最多只能上传${maxCount}张照片`,
          icon: 'none'
        });
        return;
      }

      // 使用页面扩展的上传功能
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];

      if (!currentPage || !currentPage.uploadImage) {
        wx.showToast({
          title: '上传功能不可用',
          icon: 'none'
        });
        return;
      }

      this.setData({ uploading: true });

      const { orderInfo } = this.data;
      const keyPrefix = `special-notes/${orderInfo.id}/`;

      currentPage.uploadImage(
        currentPage,
        '', // 存储字段
        keyPrefix, // 上传key前缀
        remainCount // 最大数量
      ).then(res => {
        // 将新上传的照片与已有照片合并
        const newPhotoList = [...photoList, ...res];

        // 检查是否超过最大数量限制
        const finalPhotoList = newPhotoList.slice(0, this.data.maxCount);

        this.setData({
          photoList: finalPhotoList,
          uploading: false,
          hasChanges: true
        });

        wx.showToast({
          title: `上传成功，共${finalPhotoList.length}张照片`,
          icon: 'success'
        });
      }).catch(error => {
        console.error('上传失败:', error);
        this.setData({ uploading: false });
        wx.showToast({
          title: '上传失败',
          icon: 'none'
        });
      });
    },

    /**
     * 删除图片
     */
    deletePhoto(e) {
      if (this.data.readonly) {
        return;
      }

      const index = e.currentTarget.dataset.index;
      const photoList = [...this.data.photoList];
      photoList.splice(index, 1);
      
      this.setData({
        photoList,
        hasChanges: true
      });
    },

    /**
     * 预览图片
     */
    previewImage(e) {
      const url = e.currentTarget.dataset.url;
      const { photoList } = this.data;
      
      wx.previewImage({
        current: url,
        urls: photoList
      });
    },

    /**
     * 确认提交
     */
    onConfirm() {
      if (this.data.readonly) {
        this.onCancel();
        return;
      }

      const { content, photoList } = this.data;
      
      if (!content.trim() && photoList.length === 0) {
        wx.showToast({
          title: '请输入特殊情况说明或上传图片',
          icon: 'none'
        });
        return;
      }

      this.triggerEvent('confirm', {
        content: content.trim(),
        photoList
      });
    },

    /**
     * 取消操作
     */
    onCancel() {
      this.triggerEvent('cancel');
    },

    /**
     * 删除特殊情况说明
     */
    onDelete() {
      if (this.data.readonly) {
        return;
      }

      wx.showModal({
        title: '确认删除',
        content: '确定要删除这条特殊情况说明吗？',
        success: (res) => {
          if (res.confirm) {
            this.triggerEvent('delete');
          }
        }
      });
    }
  }
});
